#!/bin/bash
set -e

# This script runs as part of PostgreSQL Docker initialization
# financial_user is created as superuser by <PERSON><PERSON> with POSTGRES_PASSWORD from .env

echo "Starting database security hardening..."

# Connect to the database and run security hardening
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Security hardening: Revoke connect privilege from public role
    REVOKE CONNECT ON DATABASE $POSTGRES_DB FROM PUBLIC;

    -- Drop the default postgres user if it exists (security hardening)
    DO \$\$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'postgres') THEN
            -- Transfer ownership of any objects owned by postgres to current superuser
            EXECUTE 'REASSIGN OWNED BY postgres TO $POSTGRES_USER';
            -- Drop any remaining privileges
            DROP OWNED BY postgres;
            -- Drop the postgres user
            DROP ROLE postgres;
            RAISE NOTICE 'Default postgres user removed for security';
        ELSE
            RAISE NOTICE 'Default postgres user does not exist (good for security)';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Could not remove postgres user: %', SQLERRM;
    END \$\$;

    -- Log successful completion
    SELECT 'Database security hardening completed' AS status;
EOSQL

echo "Database security hardening completed!"

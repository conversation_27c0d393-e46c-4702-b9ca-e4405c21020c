plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.5'
    id 'io.spring.dependency-management' version '1.1.4'
}

group = 'com.example'
version = '0.1.0'

java {
    sourceCompatibility = '17'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    // MyBatis Plus for Spring Boot 3 (includes MyBatis)
    implementation 'com.baomidou:mybatis-plus-spring-boot3-starter:3.5.9'

    // Database
    implementation 'org.postgresql:postgresql'

    // Liquibase
    implementation 'org.liquibase:liquibase-core'

    // OpenAPI 3 / Swagger
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.5.0'

    // Feign Client
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'

    // JSON Processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // MyBatis PostgreSQL Type Handlers
    implementation 'com.github.onozaty:mybatis-postgresql-typehandlers:1.0.2'

    // Test Dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.amqp:spring-rabbit-test'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:rabbitmq'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2023.0.1"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

// Gradle wrapper task
wrapper {
    gradleVersion = '8.10.2'
}

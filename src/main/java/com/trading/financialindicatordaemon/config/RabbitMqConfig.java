package com.trading.financialindicatordaemon.config;

import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.stream.Stream;

@Configuration
public class RabbitMqConfig {

    // Queue names
    public static final String MINE_USD_DATA_BY_SYMBOLS = "mine_usd_data_by_symbols";
    public static final String MINE_BTC_DATA_BY_SYMBOLS = "mine_btc_data_by_symbols";
    public static final String CREATE_INDICATOR_DATA = "create_indicator_data";
    public static final String CREATE_MAPPINGS = "create_mappings";
    public static final String MINE_ACTIVE_SYMBOLS = "mine_active_symbols";
    public static final String MINE_STOCK_DATA_BY_SYMBOLS = "mine_stock_data_by_symbols";
    public static final String CREATE_STOCK_INDICATOR_DATA = "create_stock_indicator_data";

    @Bean
    public List<Queue> queues() {
        return Stream.of(
                        MINE_USD_DATA_BY_SYMBOLS,
                        MINE_BTC_DATA_BY_SYMBOLS,
                        CREATE_INDICATOR_DATA,
                        CREATE_MAPPINGS,
                        MINE_ACTIVE_SYMBOLS,
                        MINE_STOCK_DATA_BY_SYMBOLS,
                        CREATE_STOCK_INDICATOR_DATA
                )
                .map(name -> QueueBuilder.durable(name).build())
                .toList();
    }

    // Message converter
    @Bean
    public Jackson2JsonMessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory, Jackson2JsonMessageConverter messageConverter) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter);
        return template;
    }

    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
            ConnectionFactory connectionFactory, Jackson2JsonMessageConverter messageConverter) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter);
        factory.setPrefetchCount(10);
        factory.setAcknowledgeMode(org.springframework.amqp.core.AcknowledgeMode.MANUAL);
        return factory;
    }

}

package com.trading.financialindicatordaemon.config;

import feign.RequestInterceptor;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@EnableFeignClients(basePackages = "com.trading.financialindicatordaemon.client")
@Profile("!test")
public class FeignConfig {

    @Bean
    public RequestInterceptor browserHeadersRequestInterceptor() {
        return new BrowserHeadersRequestInterceptor();
    }

}

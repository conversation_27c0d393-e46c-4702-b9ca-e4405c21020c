package com.trading.financialindicatordaemon.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;

import java.util.UUID;

public class BrowserHeadersRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        if (template.url().contains("coinmarketcap.com")) {
            template.header("Accept", "application/json, text/plain, */*");
            template.header("Accept-Language", "en-US,en;q=0.9,ru;q=0.8,et;q=0.7");
            template.header("Cache-Control", "no-cache");
            template.header("DNT", "1");
            template.header("Origin", "https://coinmarketcap.com");
            template.header("Platform", "web");
            template.header("Priority", "u=1, i");
            template.header("Referer", "https://coinmarketcap.com/");
            template.header("Sec-CH-UA", "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"");
            template.header("Sec-CH-UA-Mobile", "?0");
            template.header("Sec-CH-UA-Platform", "\"macOS\"");
            template.header("Sec-Fetch-Dest", "empty");
            template.header("Sec-Fetch-Mode", "cors");
            template.header("Sec-Fetch-Site", "same-site");
            template.header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");
            template.header("X-Request-ID", UUID.randomUUID().toString().replace("-", ""));
        }
    }

}

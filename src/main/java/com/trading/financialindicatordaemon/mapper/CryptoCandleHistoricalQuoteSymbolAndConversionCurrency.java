package com.trading.financialindicatordaemon.mapper;

public class CryptoCandleHistoricalQuoteSymbolAndConversionCurrency {
    private String symbol;
    private String conversionCurrency;

    public CryptoCandleHistoricalQuoteSymbolAndConversionCurrency() {
    }

    public CryptoCandleHistoricalQuoteSymbolAndConversionCurrency(String symbol, String conversionCurrency) {
        this.symbol = symbol;
        this.conversionCurrency = conversionCurrency;
    }

    public String getConversionCurrency() {
        return conversionCurrency;
    }

    public String getSymbol() {
        return symbol;
    }


}

package com.trading.financialindicatordaemon.mapper;

import com.trading.financialindicatordaemon.service.stock.StockCandleData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Mapper
public interface StockCandleDataMapper {

    void insert(@Param("candles") List<StockCandleData> candles);

    List<StockCandleData> findBySymbolAndConversionCurrency(@Param("symbol") String symbol,
                                                            @Param("conversionCurrency") String conversionCurrency);

    Optional<LocalDate> findLatestTimestamp(@Param("symbol") String symbol,
                                            @Param("conversionCurrency") String conversionCurrency);

    List<StockCandleDataSymbolAndConversionCurrency> findAllSymbolAndConversionCurrency();

    List<StockCandleData> findLatestQuoteForAllSymbolsAndConversionCurrencies();

}

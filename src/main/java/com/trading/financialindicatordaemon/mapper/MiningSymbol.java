package com.trading.financialindicatordaemon.mapper;

import java.time.LocalDate;

public class MiningSymbol {
    private String symbol;
    private LocalDate lastMinedDate;

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public LocalDate getLastMinedDate() {
        return lastMinedDate;
    }

    public void setLastMinedDate(LocalDate lastMinedDate) {
        this.lastMinedDate = lastMinedDate;
    }

}

package com.trading.financialindicatordaemon.mapper;

import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface IndicatorDataMapper {

    Optional<IndicatorDataWrapper> findLatestByCoinAndCurrency(@Param("symbol") String symbol,
                                                               @Param("conversionCurrency") String conversionCurrency);

    void insert(@Param("symbol") String symbol,
                @Param("conversionCurrency") String conversionCurrency,
                @Param("indicatorData") List<IndicatorData> indicatorData);

    List<IndicatorDataWrapper> findAllWithLatestIndicatorDataOnly(@Param("symbolType") String symbolType);

}

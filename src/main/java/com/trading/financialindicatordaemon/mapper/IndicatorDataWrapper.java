package com.trading.financialindicatordaemon.mapper;

import com.trading.financialindicatordaemon.client.cmc.CryptocurrencyMapping;
import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorData;

import java.util.List;

public class IndicatorDataWrapper {
    private String symbol;
    private String conversionCurrency;
    private List<IndicatorData> indicatorValues;
    private CryptocurrencyMapping mapping;

    public String getSymbol() {
        return symbol;
    }

    public String getConversionCurrency() {
        return conversionCurrency;
    }

    public List<IndicatorData> getIndicatorValues() {
        return indicatorValues;
    }

    public CryptocurrencyMapping getMapping() {
        return mapping;
    }

}

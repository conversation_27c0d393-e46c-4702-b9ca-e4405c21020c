package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper;

import java.util.List;

public class StatisticsResponse<T> {
    private List<IndicatorDataWrapper> indicatorData;
    private List<T> latestQuotes;

    public List<IndicatorDataWrapper> getIndicatorData() {
        return indicatorData;
    }

    public void setIndicatorData(List<IndicatorDataWrapper> indicatorData) {
        this.indicatorData = indicatorData;
    }

    public List<T> getLatestQuotes() {
        return latestQuotes;
    }

    public void setLatestQuotes(List<T> latestQuotes) {
        this.latestQuotes = latestQuotes;
    }

}

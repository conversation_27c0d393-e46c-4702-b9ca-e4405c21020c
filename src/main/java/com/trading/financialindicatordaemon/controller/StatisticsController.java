package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper;
import com.trading.financialindicatordaemon.service.cmc.CmcCandleDataService;
import com.trading.financialindicatordaemon.service.indicator.IndicatorDataService;
import com.trading.financialindicatordaemon.service.stock.StockCandleData;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
@Tag(name = "Statistics", description = "Cryptocurrency and stock statistics API")
public class StatisticsController {
    private static final org.slf4j.Logger logger = getLogger(StatisticsController.class);

    private final IndicatorDataService indicatorDataService;
    private final CmcCandleDataService cmcCandleDataService;
    private final StockCandleDataService stockCandleDataService;

    public StatisticsController(IndicatorDataService indicatorDataService,
                                CmcCandleDataService cmcCandleDataService,
                                StockCandleDataService stockCandleDataService) {
        this.indicatorDataService = indicatorDataService;
        this.cmcCandleDataService = cmcCandleDataService;
        this.stockCandleDataService = stockCandleDataService;
    }

    @Operation(
            summary = "Get cryptocurrency statistics",
            description = "Retrieve cryptocurrency statistics for both USD and BTC currencies"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Statistics retrieved successfully",
                    content = @Content(
                            mediaType = "application/json"
                    )
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content
            )
    })
    @GetMapping("/api/v1/crypto/statistics")
    public ResponseEntity<StatisticsResponse<CryptoCandleHistoricalQuote>> getCryptoStatistics() {
        logger.info("Fetching cryptocurrency statistics");
        List<IndicatorDataWrapper> all = indicatorDataService.findAllWithLatestIndicatorDataOnly();
        logger.info("Found {} statistics records", all.size());

        StatisticsResponse<CryptoCandleHistoricalQuote> response = new StatisticsResponse<>();
        response.setIndicatorData(all);
        response.setLatestQuotes(cmcCandleDataService.findLatestQuoteForAllSymbolsAndConversionCurrencies());

        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get stock statistics",
            description = "Retrieve stock statistics for USD currency"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Statistics retrieved successfully",
                    content = @Content(
                            mediaType = "application/json"
                    )
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content
            )
    })
    @GetMapping("/api/v1/stock/statistics")
    public ResponseEntity<StatisticsResponse<StockCandleData>> getStockStatistics() {
        logger.info("Fetching stock statistics");
        List<IndicatorDataWrapper> all = indicatorDataService.findAllWithLatestIndicatorDataOnly();
        logger.info("Found {} statistics records", all.size());

        StatisticsResponse<StockCandleData> response = new StatisticsResponse<>();
        response.setIndicatorData(all);
        response.setLatestQuotes(stockCandleDataService.findLatestQuoteForAllSymbolsAndConversionCurrencies());

        return ResponseEntity.ok(response);
    }

}

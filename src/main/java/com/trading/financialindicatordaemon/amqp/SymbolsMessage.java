package com.trading.financialindicatordaemon.amqp;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class SymbolsMessage {

    @JsonProperty("symbols")
    private List<String> symbols;

    public SymbolsMessage() {
    }

    public SymbolsMessage(List<String> symbols) {
        this.symbols = symbols;
    }

    public List<String> getSymbols() {
        return symbols;
    }

    public void setSymbols(List<String> symbols) {
        this.symbols = symbols;
    }

    @Override
    public String toString() {
        return "SymbolsMessage{" +
                "symbols=" + symbols +
                '}';
    }

}

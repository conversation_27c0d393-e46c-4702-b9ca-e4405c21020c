package com.trading.financialindicatordaemon.amqp.listener;

import com.rabbitmq.client.Channel;
import com.trading.financialindicatordaemon.amqp.SymbolsMessage;
import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.trading.financialindicatordaemon.mapper.MiningSymbol;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import com.trading.financialindicatordaemon.service.mining.MiningSymbolService;
import org.slf4j.Logger;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static org.slf4j.LoggerFactory.getLogger;

@Component
public class MineActiveSymbolsListener extends BaseRabbitMqListener {
    private static final Logger logger = getLogger(MineActiveSymbolsListener.class);

    private final MiningSymbolService miningSymbolService;
    private final RabbitMqPublisher rabbitMqPublisher;

    public MineActiveSymbolsListener(DataMiningService dataMiningService,
                                     MiningSymbolService miningSymbolService,
                                     RabbitMqPublisher rabbitMqPublisher) {
        super(dataMiningService);
        this.miningSymbolService = miningSymbolService;
        this.rabbitMqPublisher = rabbitMqPublisher;
    }

    @RabbitListener(queues = RabbitMqConfig.MINE_ACTIVE_SYMBOLS)
    public void handleMineActiveSymbols(@Payload Map<String, Object> ignoredMessage,
                                        Channel channel,
                                        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                        Message amqpMessage) {
        handleSimpleMessage("mine_active_symbols", channel, deliveryTag, amqpMessage,
                () -> {
                    List<MiningSymbol> activeSymbols = miningSymbolService.findActiveSymbols();
                    if (activeSymbols.isEmpty()) {
                        logger.info("No active symbols found for mining");
                        return;
                    }

                    List<String> symbols = activeSymbols.stream()
                            .map(MiningSymbol::getSymbol)
                            .toList();

                    logger.info("Publishing {} symbols for USD and BTC mining: {}", symbols.size(), symbols);

                    rabbitMqPublisher.mineUsdDataBySymbols(new SymbolsMessage(symbols));
                    rabbitMqPublisher.mineBtcDataBySymbols(new SymbolsMessage(symbols));

                    symbols.forEach(miningSymbolService::updateLastMinedDate);
                });
    }

}

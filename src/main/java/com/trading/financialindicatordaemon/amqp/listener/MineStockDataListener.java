package com.trading.financialindicatordaemon.amqp.listener;

import com.rabbitmq.client.Channel;
import com.trading.financialindicatordaemon.amqp.SymbolsMessage;
import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
public class MineStockDataListener extends BaseRabbitMqListener {

    private static final Logger logger = LoggerFactory.getLogger(MineStockDataListener.class);
    private final RabbitMqPublisher rabbitMqPublisher;

    public MineStockDataListener(DataMiningService dataMiningService, RabbitMqPublisher rabbitMqPublisher) {
        super(dataMiningService);
        this.rabbitMqPublisher = rabbitMqPublisher;
    }

    @RabbitListener(queues = RabbitMqConfig.MINE_STOCK_DATA_BY_SYMBOLS)
    public void handleMineStockDataBySymbols(@Payload SymbolsMessage message,
                                             Channel channel,
                                             @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                             Message amqpMessage) {
        try {
            channel.basicAck(deliveryTag, false);

            if (message.getSymbols() != null && !message.getSymbols().isEmpty()) {
                logger.info("Processing {} stock symbols", message.getSymbols().size());
                dataMiningService.mineStockSymbols(message.getSymbols());
                rabbitMqPublisher.publishCreateStockIndicatorData();
            }
        } catch (Exception e) {
            logger.error("Stock data mining failed", e);
            try {
                channel.basicReject(deliveryTag, false);
            } catch (Exception ioException) {
                throw new RuntimeException("Failed to reject message", ioException);
            }
        }
    }

}

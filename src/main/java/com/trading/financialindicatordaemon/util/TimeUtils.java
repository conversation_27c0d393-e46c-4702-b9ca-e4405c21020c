package com.trading.financialindicatordaemon.util;

import java.time.LocalDateTime;

import static java.time.ZoneOffset.UTC;

public final class TimeUtils {

    private TimeUtils() {
    }

    public static long convertToUnixTimestamp(LocalDateTime localDateTime) {
        return localDateTime.toEpochSecond(UTC);
    }

    public static LocalDateTime convertToLocalDateTime(long unixTimestamp) {
        return LocalDateTime.ofEpochSecond(unixTimestamp, 0, UTC);
    }

}

package com.trading.financialindicatordaemon.util;

import java.util.List;
import java.util.stream.IntStream;

public final class CollectionUtils {

    private CollectionUtils() {
    }

    public static <T> List<List<T>> partition(List<T> list, int size) {
        return IntStream.range(0, (list.size() + size - 1) / size)
                .mapToObj(i -> list.subList(i * size, Math.min((i + 1) * size, list.size())))
                .toList();
    }

}

package com.trading.financialindicatordaemon;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@MapperScan("com.trading.financialindicatordaemon.mapper")
@ConfigurationPropertiesScan
@EnableScheduling
public class FinancialIndicatorDaemonApplication {

    public static void main(String[] args) {
        SpringApplication.run(FinancialIndicatorDaemonApplication.class, args);
    }

}

package com.trading.financialindicatordaemon.service.cmc;

import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.mapper.CmcCandleDataMapper;
import com.trading.financialindicatordaemon.mapper.CryptoCandleHistoricalQuoteSymbolAndConversionCurrency;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.trading.financialindicatordaemon.util.CollectionUtils.partition;

@Component
public class CmcCandleDataService {

    private final CmcCandleDataMapper cmcCandleDataMapper;

    public CmcCandleDataService(CmcCandleDataMapper cmcCandleDataMapper) {
        this.cmcCandleDataMapper = cmcCandleDataMapper;
    }

    public void insert(String cryptoCurrencySymbol,
                       String conversionCurrency,
                       List<CryptoCandleHistoricalQuote> quotes) {
        partition(quotes, 2000).forEach(quoteList -> {
            cmcCandleDataMapper.insert(cryptoCurrencySymbol, conversionCurrency, quoteList);
        });
    }

    public List<CryptoCandleHistoricalQuote> find(String cryptoCurrencySymbol, String conversionCurrency) {
        return cmcCandleDataMapper.findBySymbolAndConversionCurrency(cryptoCurrencySymbol, conversionCurrency);
    }

    public Optional<LocalDateTime> findLatestCloseTimestamp(String symbol, String conversionCurrency) {
        return cmcCandleDataMapper.findLatestCloseTimestamp(symbol, conversionCurrency);
    }

    public List<CryptoCandleHistoricalQuoteSymbolAndConversionCurrency> findAllSymbolAndConversionCurrency() {
        return cmcCandleDataMapper.findAllSymbolAndConversionCurrency();
    }

    public List<CryptoCandleHistoricalQuote> findLatestQuoteForAllSymbolsAndConversionCurrencies() {
        return cmcCandleDataMapper.findLatestQuoteForAllSymbolsAndConversionCurrencies();
    }

}

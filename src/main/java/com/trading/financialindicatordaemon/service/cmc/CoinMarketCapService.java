package com.trading.financialindicatordaemon.service.cmc;

import com.trading.financialindicatordaemon.client.cmc.CoinMarketCapApiClient;
import com.trading.financialindicatordaemon.client.cmc.CoinMarketCapHistoricalApiClient;
import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuotes;
import com.trading.financialindicatordaemon.client.cmc.CryptocurrencyMapping;
import com.trading.financialindicatordaemon.client.cmc.CryptocurrencyMappings;
import com.trading.financialindicatordaemon.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CoinMarketCapService {

    private static final Logger logger = LoggerFactory.getLogger(CoinMarketCapService.class);

    private final CoinMarketCapApiClient coinMarketCapApiClient;
    private final CoinMarketCapHistoricalApiClient historicalApiClient;
    private final AppConfig appConfig;

    public CoinMarketCapService(@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
                                CoinMarketCapApiClient coinMarketCapApiClient,
                                @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
                                CoinMarketCapHistoricalApiClient historicalApiClient,
                                AppConfig appConfig) {
        this.coinMarketCapApiClient = coinMarketCapApiClient;
        this.historicalApiClient = historicalApiClient;
        this.appConfig = appConfig;
    }


    public CryptoCandleHistoricalQuotes findQuotes(Integer coinId, Integer convertId,
                                                   String timeStart, String timeEnd) {
        return historicalApiClient.getHistoricalQuotes(
                String.valueOf(coinId),
                timeStart,
                timeEnd,
                String.valueOf(convertId),
                "1d"
        ).getBody().getData();
    }

    public List<CryptocurrencyMapping> getMappings() {
        logger.info("Retrieving all cryptocurrency mappings");
        ResponseEntity<CryptocurrencyMappings> response = coinMarketCapApiClient.getCryptocurrencyMappings(
                appConfig.cmc().apiKey());

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to retrieve mappings: " + response.getStatusCode());
        }

        return response.getBody().data();
    }

}

package com.trading.financialindicatordaemon.service.yahoo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Service that uses Python yfinance via external process execution.
 * This approach leverages yfinance's curl_cffi implementation for better rate limiting.
 */
@Component
public class PythonYfinanceService {

    private static final Logger logger = LoggerFactory.getLogger(PythonYfinanceService.class);
    private static final int TIMEOUT_SECONDS = 30;
    private static final String PYTHON_SCRIPT_RESOURCE = "/yahoo_finance_fetcher.py";
    private static final String REQUIREMENTS_RESOURCE = "/requirements.txt";
    private final ObjectMapper objectMapper = new ObjectMapper();
    private Path pythonScriptPath;
    private Path requirementsPath;

    /**
     * Initialize Python resources by extracting them from JAR to temp files
     */
    private void initializePythonResources() throws IOException {
        if (pythonScriptPath == null || requirementsPath == null) {
            // Extract Python script from resources
            try (InputStream scriptStream = getClass().getResourceAsStream(PYTHON_SCRIPT_RESOURCE)) {
                if (scriptStream == null) {
                    throw new IOException("Python script resource not found: " + PYTHON_SCRIPT_RESOURCE);
                }
                pythonScriptPath = Files.createTempFile("yahoo_finance_fetcher", ".py");
                Files.copy(scriptStream, pythonScriptPath, StandardCopyOption.REPLACE_EXISTING);
                pythonScriptPath.toFile().setExecutable(true);
            }

            // Extract requirements.txt from resources
            try (InputStream reqStream = getClass().getResourceAsStream(REQUIREMENTS_RESOURCE)) {
                if (reqStream == null) {
                    throw new IOException("Requirements resource not found: " + REQUIREMENTS_RESOURCE);
                }
                requirementsPath = Files.createTempFile("requirements", ".txt");
                Files.copy(reqStream, requirementsPath, StandardCopyOption.REPLACE_EXISTING);
            }

            logger.debug("Python resources extracted to: script={}, requirements={}",
                    pythonScriptPath, requirementsPath);
        }
    }

    /**
     * Fetch stock candle data using Python yfinance
     */
    public List<YahooStockCandle> getStockCandles(String symbol, long startTimestamp, long endTimestamp) {
        logger.info("Fetching stock data via Python yfinance for symbol: {} from {} to {}",
                symbol, startTimestamp, endTimestamp);

        try {
            // Initialize Python resources
            initializePythonResources();

            // Convert timestamps to date strings
            String startDate = Instant.ofEpochSecond(startTimestamp).toString().substring(0, 10);
            String endDate = Instant.ofEpochSecond(endTimestamp).toString().substring(0, 10);

            // Build process using virtual environment
            ProcessBuilder pb = new ProcessBuilder(
                    "bash", "-c",
                    String.format("source venv/bin/activate && python3 %s %s --start %s --end %s --interval 1d",
                            pythonScriptPath.toAbsolutePath(), symbol, startDate, endDate)
            );

            // Set working directory and environment
            pb.directory(new File("."));
            pb.redirectErrorStream(false);

            logger.debug("Executing: {}", String.join(" ", pb.command()));

            Process process = pb.start();

            // Wait for completion with timeout
            boolean finished = process.waitFor(TIMEOUT_SECONDS, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                logger.error("Python yfinance process timed out for symbol: {}", symbol);
                return List.of();
            }

            int exitCode = process.exitValue();
            String output = new String(process.getInputStream().readAllBytes());
            String error = new String(process.getErrorStream().readAllBytes());

            if (exitCode != 0) {
                logger.error("Python yfinance failed for symbol {}: exit={}, error={}", symbol, exitCode, error);
                return List.of();
            }

            if (output.trim().isEmpty()) {
                logger.warn("Empty output from Python yfinance for symbol: {}", symbol);
                return List.of();
            }

            return parsePythonOutput(output, symbol);

        } catch (Exception e) {
            logger.error("Error executing Python yfinance for symbol {}: {}", symbol, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * Parse Python script JSON output into YahooStockCandle objects
     */
    private List<YahooStockCandle> parsePythonOutput(String jsonOutput, String symbol) {
        try {
            Map<String, Object> response = objectMapper.readValue(jsonOutput, new TypeReference<Map<String, Object>>() {
            });

            Boolean success = (Boolean) response.get("success");
            if (success == null || !success) {
                String error = (String) response.get("error");
                logger.error("Python yfinance returned error for {}: {}", symbol, error);
                return List.of();
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> candleDataList = (List<Map<String, Object>>) response.get("data");

            if (candleDataList == null || candleDataList.isEmpty()) {
                logger.warn("No candle data returned for symbol: {}", symbol);
                return List.of();
            }

            List<YahooStockCandle> candles = new ArrayList<>();

            for (Map<String, Object> candleData : candleDataList) {
                try {
                    LocalDateTime timestamp = LocalDateTime.ofInstant(
                            Instant.ofEpochSecond(((Number) candleData.get("timestamp")).longValue()),
                            ZoneOffset.UTC
                    );

                    YahooStockCandle candle = new YahooStockCandle(
                            (String) candleData.get("symbol"),
                            timestamp,
                            BigDecimal.valueOf(((Number) candleData.get("open")).doubleValue()),
                            BigDecimal.valueOf(((Number) candleData.get("high")).doubleValue()),
                            BigDecimal.valueOf(((Number) candleData.get("low")).doubleValue()),
                            BigDecimal.valueOf(((Number) candleData.get("close")).doubleValue()),
                            BigDecimal.valueOf(((Number) candleData.get("adjClose")).doubleValue()),
                            ((Number) candleData.get("volume")).longValue(),
                            (String) candleData.get("currency")
                    );

                    candles.add(candle);

                } catch (Exception e) {
                    logger.warn("Error parsing individual candle data for {}: {}", symbol, e.getMessage());
                }
            }

            logger.info("Successfully parsed {} candles from Python yfinance for symbol: {}", candles.size(), symbol);
            return candles;

        } catch (Exception e) {
            logger.error("Error parsing Python yfinance output for {}: {}", symbol, e.getMessage());
            return List.of();
        }
    }

    /**
     * Check if Python and yfinance are available
     */
    public boolean isAvailable() {
        try {
            // Initialize resources first
            initializePythonResources();

            ProcessBuilder pb = new ProcessBuilder("bash", "-c", "source venv/bin/activate && python3 -c 'import yfinance; print(\"OK\")'");
            pb.directory(new File("."));
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                return false;
            }

            String output = new String(process.getInputStream().readAllBytes());
            return process.exitValue() == 0 && output.trim().equals("OK");

        } catch (Exception e) {
            logger.debug("Python yfinance not available: {}", e.getMessage());
            return false;
        }
    }

}

package com.trading.financialindicatordaemon.service.mining;

import com.trading.financialindicatordaemon.mapper.MiningSymbol;
import com.trading.financialindicatordaemon.mapper.MiningSymbolMapper;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Component
public class MiningSymbolService {

    private final MiningSymbolMapper miningSymbolMapper;

    public MiningSymbolService(MiningSymbolMapper miningSymbolMapper) {
        this.miningSymbolMapper = miningSymbolMapper;
    }

    public void updateLastMinedDate(String symbol) {
        miningSymbolMapper.updateLastMinedDate(symbol, LocalDate.now());
    }

    public List<MiningSymbol> findActiveSymbols() {
        return miningSymbolMapper.findActiveSymbols();
    }

}

package com.trading.financialindicatordaemon.service.mining;

import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class ScheduledMiningService {

    private static final Logger logger = LoggerFactory.getLogger(ScheduledMiningService.class);

    private final RabbitMqPublisher rabbitMqPublisher;

    public ScheduledMiningService(RabbitMqPublisher rabbitMqPublisher) {
        this.rabbitMqPublisher = rabbitMqPublisher;
    }

    @Scheduled(cron = "0 */5 * * * *")
    public void triggerMining() {
        logger.info("Triggering scheduled mining via RabbitMQ");
        rabbitMqPublisher.publishMineActiveSymbols();
    }

}

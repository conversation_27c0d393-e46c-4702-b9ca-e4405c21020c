package com.trading.financialindicatordaemon.client.cmc;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

// fix syntax
public record CryptocurrencyMapping(
        Integer id,
        Integer rank,
        String name,
        String symbol,
        String slug,
        @JsonProperty("is_active")
        Boolean isActive,
        @JsonProperty("first_historical_data")
        LocalDateTime firstHistoricalData,
        @JsonProperty("last_historical_data")
        LocalDateTime lastHistoricalData,
        Object platform
) {
}

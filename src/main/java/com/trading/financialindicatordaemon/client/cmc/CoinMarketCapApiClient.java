package com.trading.financialindicatordaemon.client.cmc;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "coinmarketcap-api", url = "https://pro-api.coinmarketcap.com")
public interface CoinMarketCapApiClient {

    @GetMapping("/v1/cryptocurrency/map")
    ResponseEntity<CryptocurrencyMappings> getCryptocurrencyMappings(
            @RequestHeader("X-CMC_PRO_API_KEY") String apiKey
    );

}

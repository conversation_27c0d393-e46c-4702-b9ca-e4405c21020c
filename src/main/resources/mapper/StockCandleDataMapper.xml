<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.financialindicatordaemon.mapper.StockCandleDataMapper">

    <insert id="insert">
        INSERT INTO crypto_data.stock_candle_data (
        symbol,
        conversion_currency,
        timestamp,
        open,
        high,
        low,
        close,
        volume)
        VALUES
        <foreach collection="candles" item="candle" separator=",">
            (#{candle.symbol},
            #{candle.conversionCurrency},
            #{candle.timestamp},
            #{candle.open},
            #{candle.high},
            #{candle.low},
            #{candle.close},
            #{candle.volume})
        </foreach>
        ON CONFLICT (symbol, conversion_currency, timestamp) DO NOTHING;
    </insert>

    <resultMap id="stockCandleDataResultMap" type="com.trading.financialindicatordaemon.service.stock.StockCandleData">
        <result column="symbol" property="symbol"/>
        <result column="conversion_currency" property="conversionCurrency"/>
        <result column="timestamp" property="timestamp"/>
        <result column="open" property="open"/>
        <result column="high" property="high"/>
        <result column="low" property="low"/>
        <result column="close" property="close"/>
        <result column="volume" property="volume"/>
    </resultMap>

    <select id="findBySymbolAndConversionCurrency"
            resultMap="stockCandleDataResultMap">
        SELECT *
        FROM crypto_data.stock_candle_data
        WHERE symbol = #{symbol}
          AND conversion_currency = #{conversionCurrency}
        ORDER BY timestamp
    </select>

    <select id="findLatestTimestamp" resultType="java.time.LocalDate">
        SELECT MAX(timestamp) AS latest_timestamp
        FROM crypto_data.stock_candle_data
        WHERE symbol = #{symbol}
          AND conversion_currency = #{conversionCurrency}
    </select>

    <select id="findAllSymbolAndConversionCurrency"
            resultType="com.trading.financialindicatordaemon.mapper.StockCandleDataSymbolAndConversionCurrency">
        SELECT DISTINCT symbol, conversion_currency
        FROM crypto_data.stock_candle_data
    </select>

    <select id="findLatestQuoteForAllSymbolsAndConversionCurrencies"
            resultMap="stockCandleDataResultMap">
        SELECT *
        FROM crypto_data.stock_candle_data
        WHERE (symbol, conversion_currency, timestamp) IN
              (SELECT symbol, conversion_currency, MAX(timestamp) AS timestamp
               FROM crypto_data.stock_candle_data
               GROUP BY symbol, conversion_currency)
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.financialindicatordaemon.mapper.MiningSymbolMapper">

    <resultMap id="miningSymbolResultMap" type="com.trading.financialindicatordaemon.mapper.MiningSymbol">
        <result column="symbol" property="symbol"/>
        <result column="last_mined_date" property="lastMinedDate"/>
    </resultMap>

    <select id="findActiveSymbols" resultMap="miningSymbolResultMap">
        <![CDATA[
        SELECT *
        FROM crypto_data.mining_symbols
        WHERE COALESCE(last_mined_date, '-infinity'::DATE) <= NOW() - INTERVAL '1 day'
        ORDER BY symbol
        LIMIT 2
        ]]>
    </select>

    <update id="updateLastMinedDate">
        UPDATE crypto_data.mining_symbols
        SET last_mined_date = #{date}
        WHERE symbol = #{symbol}
    </update>

</mapper>

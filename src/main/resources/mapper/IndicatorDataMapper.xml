<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.financialindicatordaemon.mapper.IndicatorDataMapper">
    <insert id="insert">
        INSERT INTO crypto_data.indicator_data
            (symbol, conversion_currency, indicator_values)
        VALUES (#{symbol}, #{conversionCurrency},
                #{indicatorData,jdbcType=OTHER,typeHandler=com.trading.financialindicatordaemon.typehandler.IndicatorDataListTypeHandler}) ON CONFLICT (symbol, conversion_currency) DO
        UPDATE SET indicator_values = excluded.indicator_values;
    </insert>

    <resultMap id="indicatorDataWrapperResultMap"
               type="com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper">
        <result column="symbol" property="symbol"/>
        <result column="conversion_currency" property="conversionCurrency"/>
        <result column="indicator_values" property="indicatorValues"
                typeHandler="com.trading.financialindicatordaemon.typehandler.IndicatorDataListTypeHandler"/>
        <association property="mapping" column="symbol"
                     select="com.trading.financialindicatordaemon.mapper.CmcMappingsMapper.findBySymbol"/>
    </resultMap>

    <select id="findLatestByCoinAndCurrency" resultMap="indicatorDataWrapperResultMap">
        SELECT symbol, conversion_currency, indicator_values
        FROM crypto_data.indicator_data
        WHERE symbol = #{symbol}
          AND conversion_currency = #{conversionCurrency}
    </select>

    <select id="findAllWithLatestIndicatorDataOnly" resultMap="indicatorDataWrapperResultMap">
        SELECT symbol, conversion_currency, json_build_array((indicator_values ->> 0) ::jsonb) AS indicator_values
        FROM crypto_data.indicator_data
        <where>
            <choose>
                <when test="symbolType != null and symbolType == 'CRYPTO'">
                    AND symbol IN (
                    SELECT candle.symbol
                    FROM crypto_data.cmc_candle_data candle
                    )
                </when>
                <when test="symbolType != null and symbolType == 'STOCK'">
                    AND symbol NOT IN (
                    SELECT candle.symbol
                    FROM crypto_data.cmc_candle_data candle
                    )
                </when>
                <otherwise>
                    AND 1 = 1
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>

--liquibase formatted sql

--changeset kkoemets:baseline
CREATE EXTENSION IF NOT EXISTS hstore;

CREATE SCHEMA IF NOT EXISTS crypto_data;

CREATE TABLE crypto_data.cmc_mappings
(
    cryptocurrency_id     INTEGER,
    symbol                VARCHAR(20)  NOT NULL,
    name                  <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    slug                  VARCHAR(255) NOT NULL,
    rank                  INTEGER,
    is_active             BOOLEAN   DEFAULT FALSE,
    first_historical_data TIMESTAMP,
    last_historical_data  TIMESTAMP,
    platform              jsonb,
    created_at            TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at            TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE crypto_data.cmc_candle_data
(
    symbol              VARCHAR(20),
    conversion_currency VARCHAR(3),
    time_open           TIMESTAMP,
    time_close          TIMESTAMP,
    time_high           TIMESTAMP,
    time_low            TIMESTAMP,
    quote               jsonb,
    UNIQUE (symbol, conversion_currency, time_close)
);

CREATE TABLE crypto_data.indicator_data
(
    symbol              VA<PERSON>HA<PERSON>(20),
    conversion_currency VARCHAR(3),
    indicator_values    jsonb,
    UNIQUE (symbol, conversion_currency)
);

CREATE TABLE crypto_data.mining_symbols
(
    symbol          VARCHAR(20) NOT NULL UNIQUE,
    last_mined_date DATE
);

--changeset kkoemets:baseline-overrides
CREATE TABLE crypto_data.cmc_symbol_overrides
(
    symbol            VARCHAR(20) NOT NULL UNIQUE,
    cryptocurrency_id INTEGER     NOT NULL
);

INSERT INTO crypto_data.cmc_symbol_overrides (symbol, cryptocurrency_id)
VALUES ('BOZO', 29308);

--changeset kkoemets:stock-data
CREATE TABLE crypto_data.stock_candle_data
(
    symbol              VARCHAR(20),
    conversion_currency VARCHAR(3),
    timestamp           DATE,
    open                NUMERIC(10, 2),
    high                NUMERIC(10, 2),
    low                 NUMERIC(10, 2),
    close               NUMERIC(10, 2),
    volume              BIGINT,
    UNIQUE (symbol, conversion_currency, timestamp)
);

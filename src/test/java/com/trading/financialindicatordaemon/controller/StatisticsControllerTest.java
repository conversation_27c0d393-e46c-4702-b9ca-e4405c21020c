package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.service.indicator.IndicatorDataService;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import com.trading.financialindicatordaemon.service.yahoo.YahooFinanceService;
import com.trading.financialindicatordaemon.service.yahoo.YahooStockCandle;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static com.trading.financialindicatordaemon.config.AppConfig.USD_CURRENCY_ID;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
public class StatisticsControllerTest extends BaseTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private IndicatorDataService indicatorDataService;
    @Autowired
    private DataMiningService dataMiningService;
    @Autowired
    private StockCandleDataService stockCandleDataService;
    @Autowired
    private YahooFinanceService yahooFinanceService;

    @Test
    public void getCryptoStatistics_shouldReturnDataWhenExists() throws Exception {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), USD_CURRENCY_ID);
        indicatorDataService.calculateForCrypto("SOL", "USD");

        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", 1735430400, 1751155200);
        stockCandleDataService.insertYahoo(candles);
        indicatorDataService.calculateForStock("TSLA", "USD");

        mockMvc.perform(get("/api/v1/crypto/statistics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.indicatorData").isArray())
                .andExpect(jsonPath("$.indicatorData.length()").value(1));
    }

    @Test
    public void getStockStatistics_shouldReturnDataWhenExists() throws Exception {
        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", 1735430400, 1751155200);
        stockCandleDataService.insertYahoo(candles);
        indicatorDataService.calculateForStock("TSLA", "USD");

        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), USD_CURRENCY_ID);
        indicatorDataService.calculateForCrypto("SOL", "USD");

        mockMvc.perform(get("/api/v1/stock/statistics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.indicatorData").isArray())
                .andExpect(jsonPath("$.indicatorData.length()").value(1));
    }

}

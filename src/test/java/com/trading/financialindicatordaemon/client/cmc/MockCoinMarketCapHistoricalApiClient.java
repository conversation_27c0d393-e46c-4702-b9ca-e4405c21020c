package com.trading.financialindicatordaemon.client.cmc;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Component
public class MockCoinMarketCapHistoricalApiClient implements CoinMarketCapHistoricalApiClient {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public ResponseEntity<CryptoCandleHistoricalQuotesResponse> getHistoricalQuotes(String id, String timeStart, String timeEnd, String convert, String interval) {
        Map<String, Object> map = null;
        try {
            map = objectMapper.readValue(
                    getClass().getClassLoader().getResourceAsStream("data/candle_data/%s_%s_%s.json".formatted(id, timeStart, timeEnd)),
                    Map.class
            );
        } catch (IOException | IllegalArgumentException e) {
            return ResponseEntity.ok(new CryptoCandleHistoricalQuotesResponse(new CryptoCandleHistoricalQuotes(List.of())));
        }

        Object quotes = map.get("data");

        CryptoCandleHistoricalQuotesResponse mappings = objectMapper.convertValue(Map.of("data", quotes), CryptoCandleHistoricalQuotesResponse.class);
        return ResponseEntity.ok(mappings);
    }

}

package com.trading.financialindicatordaemon.client.indicatorapi;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

@Component
public class MockIndicatorClient implements IndicatorApiClient {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public ResponseEntity<String> checkHealth() {
        return ResponseEntity.ok("OK");
    }

    @Override
    public ResponseEntity<List<IndicatorData>> calculateIndicators(List<CalculateIndicatorsRequest> data) {
        try {
            List<IndicatorData> indicatorData = objectMapper.readValue(
                    getClass().getClassLoader().getResourceAsStream("data/indicator_data/SOL_USD.json"),
                    new TypeReference<>() {
                    }
            );
            return ResponseEntity.ok(indicatorData);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


}

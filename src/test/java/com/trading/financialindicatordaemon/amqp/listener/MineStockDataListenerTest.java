package com.trading.financialindicatordaemon.amqp.listener;

import com.rabbitmq.client.Channel;
import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.amqp.SymbolsMessage;
import com.trading.financialindicatordaemon.service.stock.StockCandleData;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.verify;

public class MineStockDataListenerTest extends BaseTest {

    @Autowired
    private MineStockDataListener mineStockDataListener;

    @Autowired
    private StockCandleDataService stockCandleDataService;

    @MockBean
    private Channel channel;

    @Test
    public void handleMineStockDataBySymbols_shouldProcessSymbols() throws Exception {
        SymbolsMessage message = new SymbolsMessage(List.of("TSLA"));

        mineStockDataListener.handleMineStockDataBySymbols(message, channel, 1L, null);

        verify(channel).basicAck(1L, false);

        List<StockCandleData> stockData = stockCandleDataService.find("TSLA", "USD");
        assertThat(stockData).isNotEmpty();
    }

}

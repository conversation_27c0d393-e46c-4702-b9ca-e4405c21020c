package com.trading.financialindicatordaemon.service.cmc;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote.Quote;
import com.trading.financialindicatordaemon.mapper.CryptoCandleHistoricalQuoteSymbolAndConversionCurrency;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class CmcCandleDataServiceTest extends BaseTest {

    @Autowired
    private CmcCandleDataService cmcCandleDataService;

    @Test
    public void insert_duplicatesAreNotInserted() {
        assertThat(cmcCandleDataService.find("BTC", "USD")).size().isEqualTo(0);
        CryptoCandleHistoricalQuote quote1 = new CryptoCandleHistoricalQuote();
        quote1.setTimeOpen("2020-08-06T00:00:00.000Z");
        quote1.setTimeClose("2020-08-06T00:00:00.000Z");
        quote1.setTimeHigh("2020-08-06T00:00:00.000Z");
        quote1.setTimeLow("2020-08-06T00:00:00.000Z");
        quote1.setQuote(new Quote());

        CryptoCandleHistoricalQuote quote2 = new CryptoCandleHistoricalQuote();
        quote2.setTimeOpen("2020-08-05T00:00:00.000Z");
        quote2.setTimeClose("2020-08-06T00:00:00.000Z");
        quote2.setTimeHigh("2020-08-05T00:00:00.000Z");
        quote2.setTimeLow("2020-08-05T00:00:00.000Z");
        quote2.setQuote(new Quote());

        cmcCandleDataService.insert("BTC", "USD", List.of(
                quote1,
                quote2
        ));

        assertThat(cmcCandleDataService.find("BTC", "USD")).size().isEqualTo(1);

        CryptoCandleHistoricalQuote quote3 = new CryptoCandleHistoricalQuote();
        quote3.setTimeOpen("2020-08-09T00:00:00.000Z");
        quote3.setTimeClose("2020-08-09T00:00:00.000Z");
        quote3.setTimeHigh("2020-08-09T00:00:00.000Z");
        quote3.setTimeLow("2020-08-09T00:00:00.000Z");
        quote3.setQuote(new Quote());

        cmcCandleDataService.insert("BTC", "USD", List.of(quote3));
        assertThat(cmcCandleDataService.find("BTC", "USD")).size().isEqualTo(2);
    }

    @Test
    public void findAllSymbolAndConversionCurrency_shouldReturnAllSymbolsAndConversionCurrencies() {
        CryptoCandleHistoricalQuote quote1 = new CryptoCandleHistoricalQuote();
        quote1.setTimeOpen("2020-08-06T00:00:00.000Z");
        quote1.setTimeClose("2020-08-06T00:00:00.000Z");
        quote1.setTimeHigh("2020-08-06T00:00:00.000Z");
        quote1.setTimeLow("2020-08-06T00:00:00.000Z");
        quote1.setQuote(new Quote());

        cmcCandleDataService.insert("BTC", "USD", List.of(
                quote1
        ));

        List<CryptoCandleHistoricalQuoteSymbolAndConversionCurrency> allSymbolAndConversionCurrency =
                cmcCandleDataService.findAllSymbolAndConversionCurrency();
        assertThat(allSymbolAndConversionCurrency).isNotEmpty();
        assertThat(allSymbolAndConversionCurrency).hasSize(1);
        assertThat(allSymbolAndConversionCurrency.getFirst().getSymbol()).isEqualTo("BTC");
        assertThat(allSymbolAndConversionCurrency.getFirst().getConversionCurrency()).isEqualTo("USD");
    }


}

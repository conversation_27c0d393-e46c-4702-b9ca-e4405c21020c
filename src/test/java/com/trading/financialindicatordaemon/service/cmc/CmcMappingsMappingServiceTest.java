package com.trading.financialindicatordaemon.service.cmc;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.cmc.CryptocurrencyMapping;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class CmcMappingsMappingServiceTest extends BaseTest {

    @Autowired
    private CmcMappingsMappingService cmcMappingsMappingService;
    @Autowired
    private DataMiningService dataMiningService;

    @Test
    public void findMapping_correctlyFindsOverridenSymbol() {
        dataMiningService.mineMappings();

        CryptocurrencyMapping mapping = cmcMappingsMappingService.findMapping("BOZO").orElseThrow();
        assertThat(mapping.id()).isEqualTo(29308);
    }

}

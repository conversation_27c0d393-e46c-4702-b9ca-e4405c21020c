package com.trading.financialindicatordaemon.service.mining;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.mapper.MiningSymbol;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;


public class MiningSymbolServiceTest extends BaseTest {

    @Autowired
    private MiningSymbolService miningSymbolService;

    @Test
    @Sql(scripts = "classpath:db/mining_symbols.sql")
    public void findActiveSymbols_shouldReturnActiveSymbols() {
        List<MiningSymbol> activeSymbols = miningSymbolService.findActiveSymbols();
        assertThat(activeSymbols).isNotEmpty();
        assertThat(activeSymbols.stream().map(MiningSymbol::getSymbol).toList()).contains("ETH", "SOL");
        assertThat(activeSymbols).hasSize(2);
    }

    @Test
    @Sql(scripts = "classpath:db/mining_symbols.sql")
    public void updateLastMinedDate_shouldUpdate() {
        assertThat(miningSymbolService.findActiveSymbols()
                .stream()
                .map(MiningSymbol::getSymbol)
                .toList())
                .contains("ETH", "SOL");

        miningSymbolService.updateLastMinedDate("ETH");

        assertThat(miningSymbolService.findActiveSymbols()
                .stream()
                .map(MiningSymbol::getSymbol)
                .toList())
                .contains("SOL");
    }

}

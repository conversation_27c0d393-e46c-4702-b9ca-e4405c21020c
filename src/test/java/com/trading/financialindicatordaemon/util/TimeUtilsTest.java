package com.trading.financialindicatordaemon.util;

import com.trading.financialindicatordaemon.BaseTest;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class TimeUtilsTest extends BaseTest {

    @Test
    public void convertToUnixTimestamp_shouldConvert() {
        assertThat(TimeUtils.convertToUnixTimestamp(
                LocalDateTime.of(2025, 6, 29, 0, 0, 0)))
                .isEqualTo(1751155200);

        assertThat(TimeUtils.convertToUnixTimestamp(
                LocalDateTime.of(2025, 6, 29, 0, 0, 0).minusMonths(6)))
                .isEqualTo(1735430400);
    }

}
